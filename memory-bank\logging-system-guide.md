# AI侧边栏扩展 - 日志系统使用指南

## 概述

本文档介绍AI侧边栏Chrome扩展的全局日志系统的使用方法和最佳实践。

## 日志系统特性

### 🎯 核心功能
- **多级别日志**：ERROR、WARN、INFO、DEBUG
- **跨上下文支持**：background、content、sidepanel
- **性能监控**：自动记录函数执行时间
- **结构化输出**：包含时间戳、上下文、堆栈信息
- **本地存储**：支持日志持久化和导出
- **智能缓存**：避免重复日志，提高性能

### 🔧 技术特点
- **Chrome扩展优化**：适配不同扩展上下文
- **中文支持**：所有日志消息使用中文
- **项目命名规范**：遵循aisp_前缀规范
- **错误处理**：完整的错误堆栈和上下文信息

## 基础使用方法

### 1. 在Background Script中使用

```javascript
// service-worker.js
// 日志系统已自动加载

// 基础日志记录
await aisp_logInfo('扩展初始化完成');
await aisp_logError('API调用失败', { error: error.message });
await aisp_logWarn('API密钥未配置', { status: keyStatus });
await aisp_logDebug('处理消息', { action: request.action });

// 性能监控
await aisp_logPerformance('内容分析', 1500, { success: true });

// API调用记录
await aisp_logAPICall('Gemini', 'POST', 'generateContent', { 
    contentLength: content.length 
});

// 用户操作记录
await aisp_logUserAction('toggle_sidepanel', { tabId: tab.id });

// 函数进入/退出记录
await aisp_logFunctionEntry('aisp_analyzeContent', { contentLength: 1000 });
await aisp_logFunctionExit('aisp_analyzeContent', { success: true });
```

### 2. 在Content Script中使用

```javascript
// content-script.js
// 使用简化的日志记录器

// 基础日志记录
contentLogger.info('内容捕获开始', { url: window.location.href });
contentLogger.error('捕获失败', { error: error.message });
contentLogger.warn('输入框未找到');
contentLogger.debug('页面结构分析完成');

// 性能监控
contentLogger.performance('页面内容捕获', 800, { 
    dataSize: contentData.length 
});

// 用户操作记录
contentLogger.userAction('input_focus', { 
    tagName: element.tagName,
    type: element.type 
});
```

### 3. 在Sidepanel中使用

```javascript
// sidepanel.js
// 使用上下文日志记录器

// 创建专用日志记录器
const sidepanelLogger = aisp_logCreateLogger('sidepanel');

// 基础日志记录
sidepanelLogger.info('侧边栏初始化开始');
sidepanelLogger.error('组件加载失败', { component: 'ChatInterface' });

// 带计时器的操作
const result = await sidepanelLogger.withTimer('模板系统初始化', async () => {
    return await aisp_initializeTemplateSystem();
});

// 性能监控
sidepanelLogger.performance('界面渲染', 200, { 
    componentsCount: 5 
});
```

### 4. 在API模块中使用

```javascript
// gemini-api.js
// 使用专用API日志记录器

const geminiLogger = aisp_logCreateLogger('gemini-api');

// API调用记录
geminiLogger.apiCall('Gemini', 'POST', 'generateContent', {
    promptLength: prompt.length,
    model: 'gemini-2.0-flash'
});

// 函数执行记录
geminiLogger.functionEntry('analyzeContent', { 
    contentLength: content.length 
});

// 错误处理
geminiLogger.error('API调用失败', {
    error: error.message,
    stack: error.stack,
    retryCount: 2
});

geminiLogger.functionExit('analyzeContent', { 
    success: true,
    duration: 1200 
});
```

## 高级功能

### 1. 日志配置管理

```javascript
// 设置日志配置
aisp_logSetConfig({
    level: AISP_LOG_LEVELS.DEBUG,    // 设置日志级别
    enableConsole: true,             // 启用控制台输出
    enableStorage: false,            // 禁用本地存储
    maxStorageEntries: 500          // 限制存储条目数
});

// 获取当前配置
const config = aisp_logGetConfig();
console.log('当前日志配置:', config);
```

### 2. 日志查询和导出

```javascript
// 获取存储的日志
const logs = await aisp_logGetStoredLogs({
    level: 'ERROR',                  // 只获取错误日志
    since: '2024-01-01T00:00:00Z',  // 指定时间范围
    context: 'background'            // 指定上下文
});

// 导出日志
const jsonLogs = await aisp_logExportLogs({ 
    format: 'json',
    filter: { level: 'ERROR' }
});

const textLogs = await aisp_logExportLogs({ 
    format: 'text',
    filter: { context: 'content' }
});

// 清理日志
await aisp_logClearStoredLogs();
```

### 3. 创建专用日志记录器

```javascript
// 为特定组件创建日志记录器
const templateLogger = aisp_logCreateLogger('template-system');
const driveLogger = aisp_logCreateLogger('google-drive');
const performanceLogger = aisp_logCreateLogger('performance');

// 使用专用记录器
templateLogger.info('模板加载完成', { count: templates.length });
driveLogger.error('同步失败', { error: syncError.message });
performanceLogger.performance('缓存清理', 50, { itemsCleared: 100 });
```

## 最佳实践

### 1. 日志级别使用指南

- **ERROR**：系统错误、API调用失败、关键功能异常
- **WARN**：配置问题、性能警告、非关键错误
- **INFO**：重要操作、状态变更、用户操作
- **DEBUG**：详细执行流程、变量状态、调试信息

### 2. 性能考虑

```javascript
// ✅ 推荐：使用异步日志
await aisp_logInfo('操作完成');

// ✅ 推荐：避免在循环中记录详细日志
for (let i = 0; i < items.length; i++) {
    // 只在关键点记录日志
    if (i % 100 === 0) {
        aisp_logDebug(`处理进度: ${i}/${items.length}`);
    }
}

// ✅ 推荐：使用计时器记录性能
const result = await aisp_logWithTimer('数据处理', async () => {
    return await processData(data);
});
```

### 3. 错误处理集成

```javascript
// ✅ 推荐：完整的错误日志
try {
    const result = await apiCall();
    aisp_logInfo('API调用成功', { resultSize: result.length });
} catch (error) {
    aisp_logError('API调用失败', {
        error: error.message,
        stack: error.stack,
        url: apiUrl,
        retryCount: currentRetry
    });
    throw error;
}
```

### 4. 用户隐私保护

```javascript
// ✅ 推荐：过滤敏感信息
aisp_logInfo('用户登录', { 
    userId: user.id,
    // 不记录密码或敏感信息
    timestamp: Date.now()
});

// ❌ 避免：记录敏感数据
// aisp_logInfo('用户数据', { password: user.password }); // 不要这样做
```

## 故障排除

### 1. 日志不显示
- 检查日志级别配置
- 确认控制台输出已启用
- 验证日志函数调用语法

### 2. 性能影响
- 降低日志级别（生产环境使用INFO或WARN）
- 禁用不必要的存储功能
- 避免在高频函数中记录详细日志

### 3. 跨上下文日志丢失
- 确认background script正确处理log_message
- 检查消息传递是否正常
- 验证权限配置

## 总结

日志系统为AI侧边栏扩展提供了强大的调试和监控能力。通过合理使用不同级别的日志记录，可以有效提高开发效率和问题定位能力。记住始终遵循项目的命名规范和最佳实践。
