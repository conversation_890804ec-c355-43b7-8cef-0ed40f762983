<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Side Panel</title>
    <link rel="stylesheet" href="sidepanel.css">
    <link rel="stylesheet" href="../../styles/common.css">
</head>
<body>
    <div id="aisp-main" class="aisp-main">
        <!-- 头部工具栏 -->
        <header id="aisp-header" class="aisp-header">
            <div class="aisp-header-left">
                <div class="aisp-logo">
                    <img src="../../icons/icon48.png" alt="AI Side Panel" class="aisp-logo-img">
                    <h1 class="aisp-title" data-i18n="sidepanel.title">AI Side Panel</h1>
                </div>
            </div>
            
            <div class="aisp-header-right">
                <!-- 语言切换 -->
                <div class="aisp-language-selector">
                    <select id="aisp-language-select" class="aisp-select">
                        <option value="zh_CN">中文</option>
                        <option value="en_US">English</option>
                        <option value="ja_JP">日本語</option>
                        <option value="ko_KR">한국어</option>
                    </select>
                </div>

                <!-- 悬浮显示的操作按钮 -->
                <div class="aisp-header-actions">
                    <!-- 设置按钮 -->
                    <button id="aisp-settings-btn" class="aisp-icon-btn" data-i18n-title="common.settings" title="设置">
                        <span class="aisp-icon">⚙️</span>
                    </button>

                    <!-- 刷新按钮 -->
                    <button id="aisp-refresh-btn" class="aisp-icon-btn" data-i18n-title="common.refresh" title="刷新分析">
                        <span class="aisp-icon">🔄</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main id="aisp-content" class="aisp-content">
            <!-- 聊天界面容器 -->
            <div id="aisp-chat-container" class="aisp-chat-container">
                <!-- 消息列表 -->
                <div id="aisp-message-list" class="aisp-message-list">
                    <!-- 欢迎消息 -->
                    <div class="aisp-message aisp-message-system">
                        <div class="aisp-message-content">
                            <div class="aisp-message-text">
                                <h3 data-i18n="sidepanel.welcome">欢迎使用 AI Side Panel</h3>
                                <p data-i18n="sidepanel.welcomeDescription">我将帮助您分析当前页面内容，提供智能回复建议和快捷模板功能。</p>
                                <div class="aisp-quick-actions">
                                    <button id="aisp-analyze-current-page" class="aisp-btn aisp-btn-primary">
                                        <span class="aisp-btn-icon">🤖</span>
                                        <span data-i18n="sidepanel.analyzeCurrentPage">分析当前页面</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 加载状态 -->
                <div id="aisp-loading" class="aisp-loading" style="display: none;">
                    <div class="aisp-loading-spinner"></div>
                    <div class="aisp-loading-text" data-i18n="sidepanel.analyzing">AI正在分析中...</div>
                </div>

                <!-- 分析结果展示区 -->
                <div id="aisp-analysis-result" class="aisp-analysis-result" style="display: none;">
                    <!-- 页面总结 -->
                    <div class="aisp-result-section">
                        <h4 class="aisp-result-title" data-i18n="analysisPageSummary">📄 页面总结</h4>
                        <div id="aisp-summary-content" class="aisp-result-content">
                            <!-- 总结内容将在这里动态插入 -->
                        </div>
                    </div>

                    <!-- 关键要点 -->
                    <div class="aisp-result-section">
                        <h4 class="aisp-result-title" data-i18n="analysisKeyPoints">🎯 关键要点</h4>
                        <div id="aisp-keypoints-content" class="aisp-result-content">
                            <!-- 要点内容将在这里动态插入 -->
                        </div>
                    </div>

                    <!-- 思维导图 -->
                    <div class="aisp-result-section">
                        <h4 class="aisp-result-title" data-i18n="analysisMindMap">🧠 思维导图</h4>
                        <div id="aisp-mindmap-container" class="aisp-mindmap-container">
                            <!-- 思维导图将在这里渲染 -->
                        </div>
                    </div>

                    <!-- 智能回复建议 -->
                    <div class="aisp-result-section">
                        <div class="aisp-result-header">
                            <h4 class="aisp-result-title" data-i18n="analysisReplySuggestions">💬 智能回复建议</h4>
                            <div id="aisp-reply-language-selector" class="aisp-reply-language-selector">
                                <!-- 回复语言选择器将在这里动态插入 -->
                            </div>
                        </div>
                        <div id="aisp-reply-suggestions" class="aisp-reply-suggestions">
                            <!-- 回复建议将在这里动态插入 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 输入区域 -->
            <div id="aisp-input-area" class="aisp-input-area">
                <div class="aisp-input-container">
                    <textarea
                        id="aisp-user-input"
                        class="aisp-textarea"
                        data-i18n-placeholder="sidepanel.inputPlaceholder"
                        placeholder="输入您的问题或需要分析的内容..."
                        rows="3"
                    ></textarea>

                    <div class="aisp-input-actions">
                        <!-- 输入增强功能 -->
                        <div class="aisp-input-enhancement">
                            <div class="aisp-input-hint">
                                <span class="aisp-input-hint-key">⌘</span>
                                <span class="aisp-input-hint-key">Enter</span>
                                <span data-i18n="common.send">发送</span>
                            </div>
                        </div>

                        <button id="aisp-clear-btn" class="aisp-btn aisp-btn-secondary">
                            <span class="aisp-btn-icon">🗑️</span>
                            <span data-i18n="common.clear">清空</span>
                        </button>

                        <button id="aisp-send-btn" class="aisp-btn aisp-btn-primary">
                            <span class="aisp-btn-icon">📤</span>
                            <span data-i18n="common.send">发送</span>
                        </button>
                    </div>
                </div>
            </div>
        </main>

        <!-- 底部状态栏 -->
        <footer id="aisp-footer" class="aisp-footer">
            <div class="aisp-status-info">
                <span id="aisp-connection-status" class="aisp-status-item">
                    <span class="aisp-status-dot aisp-status-disconnected"></span>
                    未连接
                </span>
                
                <span id="aisp-page-info" class="aisp-status-item">
                    <span class="aisp-icon">📄</span>
                    <span id="aisp-page-title">等待页面加载...</span>
                </span>
            </div>
            
            <div class="aisp-footer-actions">
                <button id="aisp-template-manager-btn" class="aisp-text-btn"
                        data-i18n-title="templates.manager"
                        data-i18n="templates.manager"
                        title="模板管理">
                    模板管理
                </button>

                <button id="aisp-knowledge-base-btn" class="aisp-text-btn"
                        data-i18n-title="knowledgeBase.title"
                        data-i18n="knowledgeBase.title"
                        title="知识库">
                    知识库
                </button>
            </div>
        </footer>
    </div>

    <!-- 模态框容器 -->
    <div id="aisp-modal-overlay" class="aisp-modal-overlay" style="display: none;">
        <div id="aisp-modal" class="aisp-modal">
            <div class="aisp-modal-header">
                <h3 id="aisp-modal-title" class="aisp-modal-title">标题</h3>
                <button id="aisp-modal-close" class="aisp-modal-close">×</button>
            </div>
            <div id="aisp-modal-content" class="aisp-modal-content">
                <!-- 模态框内容将在这里动态插入 -->
            </div>
            <div class="aisp-modal-footer">
                <button id="aisp-modal-cancel" class="aisp-btn aisp-btn-secondary">取消</button>
                <button id="aisp-modal-confirm" class="aisp-btn aisp-btn-primary">确认</button>
            </div>
        </div>
    </div>

    <!-- 脚本文件 -->
    <!-- API配置（必须最先加载） -->
    <script src="../config/api-keys.js"></script>

    <!-- 工具脚本 -->
    <script src="../utils/common-utils.js"></script>
    <script src="../utils/language-manager.js"></script>
    <script src="../utils/config-manager.js"></script>
    <script src="../utils/gemini-api.js"></script>
    <script src="../utils/api-manager.js"></script>
    <script src="../utils/reply-generator.js"></script>
    <script src="../utils/template-manager.js"></script>
    <script src="../utils/google-drive-api.js"></script>
    <script src="../utils/cache-manager.js"></script>
    <script src="../utils/performance-optimizer.js"></script>
    <script src="../utils/test-framework.js"></script>

    <!-- 组件脚本 -->
    <script src="../components/reply-language-selector.js"></script>
    <script src="../components/chat-interface.js"></script>
    <script src="../components/mindmap-renderer.js"></script>
    <script src="../components/reply-suggestions.js"></script>
    <script src="../components/template-popup.js"></script>
    <script src="../components/knowledge-base.js"></script>
    <script src="../components/performance-monitor.js"></script>

    <!-- 主要脚本 -->
    <script src="sidepanel.js"></script>
</body>
</html>
