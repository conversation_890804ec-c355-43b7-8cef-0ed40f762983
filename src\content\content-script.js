/**
 * @file AI Side Panel 内容脚本
 * @description 注入到网页中的脚本，负责内容捕获、输入框检测和模板弹窗显示
 */

// #region 日志系统初始化
// 由于content script无法使用importScripts，我们需要创建简化的日志函数
let contentLogger = null;

/**
 * @function aisp_initializeContentLogger - 初始化内容脚本日志系统
 * @description 创建适用于content script的日志记录器
 */
function aisp_initializeContentLogger() {
    contentLogger = {
        error: (message, data = null) => {
            console.error(`[AISP-CONTENT] [ERROR] ${message}`, data);
            aisp_sendLogToBackground('ERROR', message, data);
        },
        warn: (message, data = null) => {
            console.warn(`[AISP-CONTENT] [WARN] ${message}`, data);
            aisp_sendLogToBackground('WARN', message, data);
        },
        info: (message, data = null) => {
            console.info(`[AISP-CONTENT] [INFO] ${message}`, data);
            aisp_sendLogToBackground('INFO', message, data);
        },
        debug: (message, data = null) => {
            console.log(`[AISP-CONTENT] [DEBUG] ${message}`, data);
            aisp_sendLogToBackground('DEBUG', message, data);
        },
        performance: (operation, duration, data = {}) => {
            const perfData = { operation, duration, ...data };
            console.log(`[AISP-CONTENT] [PERF] ${operation}: ${duration}ms`, perfData);
            aisp_sendLogToBackground('INFO', `性能监控: ${operation} 耗时 ${duration}ms`, perfData);
        },
        userAction: (action, details = {}) => {
            const actionData = { action, timestamp: Date.now(), ...details };
            console.log(`[AISP-CONTENT] [USER] ${action}`, actionData);
            aisp_sendLogToBackground('INFO', `用户操作: ${action}`, actionData);
        }
    };
}

/**
 * @function aisp_sendLogToBackground - 发送日志到后台脚本
 * @param {string} level - 日志级别
 * @param {string} message - 日志消息
 * @param {Object} data - 附加数据
 */
function aisp_sendLogToBackground(level, message, data) {
    try {
        chrome.runtime.sendMessage({
            action: 'log_message',
            level: level,
            message: message,
            data: data,
            context: 'content',
            timestamp: new Date().toISOString(),
            url: window.location.href
        }).catch(() => {
            // 忽略发送失败的错误，避免循环日志
        });
    } catch (error) {
        // 忽略发送失败的错误
    }
}

// 立即初始化日志系统
aisp_initializeContentLogger();
// #endregion

// #region 全局变量和配置
let aisp_contentObserver = null;
let aisp_intersectionObserver = null;
let aisp_inputFocusListener = null;
let aisp_templatePopup = null;
let aisp_isInitialized = false;
let aisp_pageStructure = null;
let aisp_contentCache = new Map();
let aisp_lastCaptureTime = 0;
let aisp_initStartTime = Date.now();

// 性能监控
let aisp_performanceMetrics = {
    captureCount: 0,
    averageTime: 0,
    lastCaptureTime: 0,
    errorCount: 0
};

// 配置常量
const AISP_CONFIG = {
    CONTENT_CAPTURE_DELAY: 1000, // 内容捕获延迟（毫秒）
    INPUT_DETECTION_DELAY: 300,  // 输入框检测延迟（毫秒）
    POPUP_SHOW_DELAY: 500,       // 弹窗显示延迟（毫秒）
    MAX_TEXT_LENGTH: 10000,      // 最大文本长度
    MAX_IMAGES: 20,              // 最大图片数量
    MIN_IMAGE_SIZE: 50,          // 最小图片尺寸
    CONTENT_SCORE_THRESHOLD: 0.3 // 内容重要性阈值
};

// 内容识别选择器
const CONTENT_SELECTORS = {
    // 主要内容区域
    main: [
        'article', 'main', '[role="main"]', '.content', '.post', '.article',
        '.entry', '.story', '.news-article', '.blog-post', '.page-content',
        '.main-content', '.primary-content', '.article-content'
    ],
    // 需要排除的区域
    exclude: [
        'nav', 'aside', 'footer', 'header', '.sidebar', '.menu', '.navigation',
        '.ad', '.advertisement', '.banner', '.popup', '.modal', '.overlay',
        '.social-share', '.related-posts', '.comments-section', '.breadcrumb',
        '.cookie-notice', '.newsletter', '.subscription', '.promo'
    ],
    // 富文本编辑器
    richEditors: [
        '.tox-tinymce', '.cke_editable', '.ql-editor', '.fr-element',
        '.note-editable', '.wysiwyg', '.rich-text', '.editor-content',
        '.medium-editor', '.summernote', '.trumbowyg-editor'
    ],
    // 结构化内容
    structured: {
        headings: 'h1, h2, h3, h4, h5, h6',
        lists: 'ul, ol, dl',
        tables: 'table',
        code: 'pre, code, .highlight, .code-block',
        quotes: 'blockquote, .quote'
    }
};

// 内容类型检测规则
const CONTENT_TYPE_RULES = {
    article: {
        selectors: ['article', '.post', '.blog-post', '.news-article'],
        indicators: ['h1', 'h2', 'p', '.author', '.date', '.publish-date', '.byline'],
        weight: 1.0
    },
    product: {
        selectors: ['.product', '.item', '.listing', '.product-detail'],
        indicators: ['.price', '.rating', '.reviews', '.add-to-cart', '.buy-now', '.product-title'],
        weight: 0.9
    },
    social: {
        selectors: ['.post', '.tweet', '.status', '.update', '.feed-item'],
        indicators: ['.like', '.share', '.comment', '.follow', '.user-avatar', '.timestamp'],
        weight: 0.8
    },
    form: {
        selectors: ['form', '.form', '.contact-form', '.signup-form'],
        indicators: ['input', 'textarea', 'select', 'button[type="submit"]', '.form-field'],
        weight: 0.7
    },
    documentation: {
        selectors: ['.docs', '.documentation', '.manual', '.guide'],
        indicators: ['.toc', '.table-of-contents', 'pre', 'code', '.example'],
        weight: 0.9
    }
};
// #endregion

// #region 初始化函数
/**
 * @function aisp_initializeContentScript - 初始化内容脚本
 * @description 设置内容监听器和输入框检测
 */
async function aisp_initializeContentScript() {
    if (aisp_isInitialized) return;

    try {
        contentLogger.info('AI Side Panel Content Script 开始初始化', {
            url: window.location.href,
            title: document.title
        });

        // 初始化页面结构分析
        await contentLogger.performance('页面结构分析', await aisp_measureTime(aisp_analyzePageStructure));

        // 初始化内容捕获
        await contentLogger.performance('内容捕获设置', await aisp_measureTime(aisp_setupContentCapture));

        // 初始化输入框检测
        await contentLogger.performance('输入框检测设置', await aisp_measureTime(aisp_setupInputDetection));

        // 初始化模板弹窗系统
        await contentLogger.performance('模板弹窗设置', await aisp_measureTime(aisp_setupTemplatePopup));

        // 初始化性能监控
        await contentLogger.performance('性能监控设置', await aisp_measureTime(aisp_setupPerformanceMonitoring));

        // 初始化错误处理
        await contentLogger.performance('错误处理设置', await aisp_measureTime(aisp_setupErrorHandling));

        aisp_isInitialized = true;
        contentLogger.info('AI Side Panel Content Script 初始化完成', {
            initializationTime: Date.now() - aisp_initStartTime
        });
    } catch (error) {
        contentLogger.error('AI Side Panel Content Script 初始化失败', {
            error: error.message,
            stack: error.stack
        });
        aisp_handleInitializationError(error);
    }
}

/**
 * @function aisp_measureTime - 测量函数执行时间
 * @param {Function} fn - 要测量的函数
 * @returns {Promise<number>} 执行时间（毫秒）
 */
async function aisp_measureTime(fn) {
    const startTime = performance.now();
    try {
        if (typeof fn === 'function') {
            await fn();
        }
        return performance.now() - startTime;
    } catch (error) {
        contentLogger.error('函数执行失败', { error: error.message });
        return performance.now() - startTime;
    }
}

/**
 * @function aisp_setupContentCapture - 设置内容捕获
 * @description 监听页面内容变化并捕获相关信息
 */
function aisp_setupContentCapture() {
    // 立即捕获当前页面内容
    aisp_capturePageContent();

    // 设置智能内容变化监听器
    aisp_contentObserver = new MutationObserver(
        util_debounce(aisp_handleContentChange, AISP_CONFIG.CONTENT_CAPTURE_DELAY)
    );

    aisp_contentObserver.observe(document.body, {
        childList: true,
        subtree: true,
        characterData: true,
        attributes: true,
        attributeFilter: ['class', 'id', 'style']
    });

    // 设置可见区域监听器
    aisp_setupIntersectionObserver();

    // 监听页面滚动和窗口大小变化
    window.addEventListener('scroll', util_debounce(aisp_handleViewportChange, 300));
    window.addEventListener('resize', util_debounce(aisp_handleViewportChange, 300));
}

/**
 * @function aisp_setupInputDetection - 设置输入框检测
 * @description 监听输入框焦点事件，用于显示模板弹窗
 */
function aisp_setupInputDetection() {
    aisp_inputFocusListener = util_debounce(aisp_handleInputFocus, AISP_CONFIG.INPUT_DETECTION_DELAY);
    
    // 监听所有输入框的焦点事件
    document.addEventListener('focusin', (event) => {
        if (aisp_isInputElement(event.target)) {
            aisp_inputFocusListener(event.target);
        }
    });
    
    // 监听失去焦点事件
    document.addEventListener('focusout', (event) => {
        if (aisp_isInputElement(event.target)) {
            setTimeout(() => aisp_hideTemplatePopup(), 200);
        }
    });
}

/**
 * @function aisp_setupTemplatePopup - 设置模板弹窗系统
 * @description 初始化模板弹窗的DOM结构
 */
function aisp_setupTemplatePopup() {
    // 创建模板弹窗容器
    aisp_templatePopup = document.createElement('div');
    aisp_templatePopup.id = 'aisp-template-popup';
    aisp_templatePopup.className = 'aisp-template-popup';
    aisp_templatePopup.style.display = 'none';
    
    document.body.appendChild(aisp_templatePopup);
}
// #endregion

// #region 内容捕获功能
/**
 * @function aisp_capturePageContent - 捕获页面内容
 * @description 提取页面的文字和图片内容，包含智能分析
 */
async function aisp_capturePageContent() {
    const startTime = performance.now();

    try {
        contentLogger.debug('开始捕获页面内容', {
            url: window.location.href,
            title: document.title
        });

        // 检查是否需要重新捕获
        if (!aisp_shouldRecapture()) {
            contentLogger.debug('跳过内容捕获，内容未发生变化');
            return;
        }

        // 识别主要内容区域
        const mainContent = aisp_identifyMainContent();
        contentLogger.debug('主要内容识别完成', {
            textLength: mainContent?.text?.length || 0
        });

        // 提取结构化内容
        const structuredContent = aisp_extractStructuredContent();
        contentLogger.debug('结构化内容提取完成', {
            headingsCount: structuredContent?.headings?.length || 0
        });

        // 检测内容类型
        const contentType = aisp_detectContentType();
        contentLogger.debug('内容类型检测完成', { contentType });

        // 提取元数据
        const metadata = aisp_extractMetadata();

        const contentData = {
            url: window.location.href,
            title: document.title,
            contentType: contentType,
            mainContent: mainContent,
            structuredContent: structuredContent,
            metadata: metadata,
            text: aisp_extractText(),
            images: aisp_extractImages(),
            pageStructure: aisp_pageStructure,
            timestamp: Date.now(),
            captureId: aisp_generateCaptureId()
        };

        // 缓存内容数据
        aisp_cacheContentData(contentData);

        contentLogger.info('内容数据准备完成', {
            textLength: contentData.text?.length || 0,
            imagesCount: contentData.images?.length || 0,
            contentType: contentData.contentType
        });

        // 发送内容到后台脚本
        const response = await chrome.runtime.sendMessage({
            action: 'content_captured',
            data: contentData
        });

        if (!response.success) {
            contentLogger.error('内容捕获失败', { error: response.error });
            aisp_performanceMetrics.errorCount++;
        } else {
            contentLogger.info('内容捕获成功发送到后台');
        }

        // 更新性能指标
        const duration = performance.now() - startTime;
        aisp_updatePerformanceMetrics(startTime);
        contentLogger.performance('页面内容捕获', duration, {
            success: response.success,
            dataSize: JSON.stringify(contentData).length
        });

    } catch (error) {
        const duration = performance.now() - startTime;
        contentLogger.error('捕获页面内容时出错', {
            error: error.message,
            stack: error.stack,
            duration
        });
        aisp_performanceMetrics.errorCount++;
        aisp_handleCaptureError(error);
    }
}

/**
 * @function content_extractText - 提取页面文字内容
 * @returns {string} 提取的文字内容
 */
/**
 * @function aisp_extractText - 提取页面文字内容（增强版）
 * @returns {string} 提取的文字内容
 */
function aisp_extractText() {
    // 优先从主内容区域提取
    const mainContent = aisp_identifyMainContent();
    if (mainContent && mainContent.text) {
        return mainContent.text.length > AISP_CONFIG.MAX_TEXT_LENGTH ?
               mainContent.text.substring(0, AISP_CONFIG.MAX_TEXT_LENGTH) + '...' :
               mainContent.text;
    }

    // 回退到传统方法
    const excludeSelectors = 'script, style, noscript, iframe, .aisp-template-popup, nav, header, footer, .advertisement, .ad, .sidebar, .menu';
    const excludeElements = document.querySelectorAll(excludeSelectors);

    // 临时隐藏排除的元素
    const hiddenElements = [];
    excludeElements.forEach(el => {
        if (el.style.display !== 'none') {
            hiddenElements.push({
                element: el,
                originalDisplay: el.style.display
            });
            el.style.display = 'none';
        }
    });

    // 提取可见文本
    const textContent = document.body.innerText || document.body.textContent || '';

    // 恢复隐藏的元素
    hiddenElements.forEach(item => {
        item.element.style.display = item.originalDisplay;
    });

    // 清理和格式化文本
    let text = textContent
        .replace(/\s+/g, ' ')           // 合并多个空白字符
        .replace(/\n\s*\n/g, '\n')      // 合并多个换行
        .trim();                        // 去除首尾空白

    // 限制长度
    if (text.length > AISP_CONFIG.MAX_TEXT_LENGTH) {
        text = text.substring(0, AISP_CONFIG.MAX_TEXT_LENGTH) + '...';
    }

    return text;
}

/**
 * @function content_extractText - 保持向后兼容的文本提取函数
 * @returns {string} 提取的文字内容
 */
function content_extractText() {
    return aisp_extractText();
}

/**
 * @function aisp_extractImages - 提取页面图片信息（增强版）
 * @returns {Array} 图片信息数组
 */
function aisp_extractImages() {
    const images = [];
    const imgElements = document.querySelectorAll('img');

    imgElements.forEach((img, index) => {
        if (aisp_isValidImageElement(img)) {
            images.push({
                src: img.src,
                alt: img.alt || '',
                width: img.offsetWidth || img.naturalWidth,
                height: img.offsetHeight || img.naturalHeight,
                title: img.title || '',
                loading: img.loading || '',
                index: index,
                importance: aisp_calculateImageImportance(img)
            });
        }
    });

    // 按重要性排序并限制数量
    return images
        .sort((a, b) => b.importance - a.importance)
        .slice(0, AISP_CONFIG.MAX_IMAGES);
}

/**
 * @function content_extractImages - 保持向后兼容的图片提取函数
 * @returns {Array} 图片信息数组
 */
function content_extractImages() {
    return aisp_extractImages();
}
// #endregion

// #region 输入框检测和模板功能
/**
 * @function aisp_isInputElement - 检查元素是否为输入框
 * @param {Element} element - 要检查的元素
 * @returns {boolean} 是否为输入框
 */
function aisp_isInputElement(element) {
    const inputTypes = ['input', 'textarea'];
    const editableTypes = ['text', 'email', 'search', 'url', 'tel'];
    
    if (inputTypes.includes(element.tagName.toLowerCase())) {
        if (element.tagName.toLowerCase() === 'input') {
            return editableTypes.includes(element.type) || !element.type;
        }
        return true;
    }
    
    return element.contentEditable === 'true';
}

/**
 * @function aisp_handleInputFocus - 处理输入框焦点事件
 * @param {Element} inputElement - 获得焦点的输入框元素
 */
async function aisp_handleInputFocus(inputElement) {
    try {
        contentLogger.userAction('input_focus', {
            tagName: inputElement.tagName,
            type: inputElement.type,
            id: inputElement.id,
            className: inputElement.className
        });

        // 获取模板数据
        const response = await chrome.runtime.sendMessage({
            action: 'get_templates'
        });

        if (response.success && response.data.length > 0) {
            contentLogger.info('显示模板弹窗', {
                templatesCount: response.data.length
            });
            aisp_showTemplatePopup(inputElement, response.data);
        } else {
            contentLogger.debug('无可用模板或获取失败', {
                success: response.success,
                error: response.error
            });
        }
    } catch (error) {
        contentLogger.error('处理输入框焦点事件失败', {
            error: error.message,
            stack: error.stack
        });
    }
}

/**
 * @function aisp_showTemplatePopup - 显示模板弹窗
 * @param {Element} inputElement - 目标输入框
 * @param {Array} templates - 模板数据
 */
function aisp_showTemplatePopup(inputElement, templates) {
    if (!aisp_templatePopup) return;
    
    // 计算弹窗位置
    const rect = inputElement.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
    
    // 设置弹窗位置
    aisp_templatePopup.style.position = 'absolute';
    aisp_templatePopup.style.top = (rect.top + scrollTop - 100) + 'px';
    aisp_templatePopup.style.left = (rect.left + scrollLeft) + 'px';
    aisp_templatePopup.style.zIndex = '10000';
    
    // 生成模板列表HTML
    const templateHTML = templates.map((template, index) => `
        <div class="aisp-template-item" data-template-id="${template.id}" data-index="${index}">
            <div class="aisp-template-title">${template.title || '未命名模板'}</div>
            <div class="aisp-template-preview">${(template.content || '').substring(0, 50)}...</div>
        </div>
    `).join('');
    
    aisp_templatePopup.innerHTML = `
        <div class="aisp-template-header">快捷回复模板</div>
        <div class="aisp-template-list">${templateHTML}</div>
        <div class="aisp-template-footer">
            <small>点击选择模板，按Tab键插入</small>
        </div>
    `;
    
    // 添加点击事件监听
    aisp_templatePopup.addEventListener('click', (event) => {
        const templateItem = event.target.closest('.aisp-template-item');
        if (templateItem) {
            const templateId = templateItem.dataset.templateId;
            const template = templates.find(t => t.id === templateId);
            if (template) {
                aisp_insertTemplate(inputElement, template.content);
                aisp_hideTemplatePopup();
            }
        }
    });
    
    // 显示弹窗
    aisp_templatePopup.style.display = 'block';
}

/**
 * @function aisp_hideTemplatePopup - 隐藏模板弹窗
 */
function aisp_hideTemplatePopup() {
    if (aisp_templatePopup) {
        aisp_templatePopup.style.display = 'none';
    }
}

/**
 * @function aisp_insertTemplate - 插入模板内容到输入框
 * @param {Element} inputElement - 目标输入框
 * @param {string} content - 要插入的内容
 */
function aisp_insertTemplate(inputElement, content) {
    if (inputElement.tagName.toLowerCase() === 'input' || inputElement.tagName.toLowerCase() === 'textarea') {
        inputElement.value = content;
        inputElement.dispatchEvent(new Event('input', { bubbles: true }));
    } else if (inputElement.contentEditable === 'true') {
        inputElement.textContent = content;
        inputElement.dispatchEvent(new Event('input', { bubbles: true }));
    }
    
    // 设置焦点到输入框末尾
    inputElement.focus();
    if (inputElement.setSelectionRange) {
        inputElement.setSelectionRange(content.length, content.length);
    }
}
// #endregion

// #region 工具函数
/**
 * @function util_debounce - 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
function util_debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
// #endregion

// #region 脚本启动
// 等待DOM加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', aisp_initializeContentScript);
} else {
    aisp_initializeContentScript();
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (aisp_contentObserver) {
        aisp_contentObserver.disconnect();
    }
    if (aisp_templatePopup) {
        aisp_templatePopup.remove();
    }
});

// #region 智能内容分析功能

/**
 * @function aisp_analyzePageStructure - 分析页面结构
 * @description 分析页面的整体结构和布局
 */
function aisp_analyzePageStructure() {
    aisp_pageStructure = {
        hasHeader: !!document.querySelector('header, .header, #header'),
        hasNavigation: !!document.querySelector('nav, .nav, .navigation'),
        hasMain: !!document.querySelector('main, .main, .content'),
        hasSidebar: !!document.querySelector('aside, .sidebar, .side'),
        hasFooter: !!document.querySelector('footer, .footer, #footer'),
        contentAreas: aisp_identifyContentAreas(),
        pageType: aisp_guessPageType(),
        language: aisp_detectLanguage(),
        readingTime: aisp_estimateReadingTime()
    };
}

/**
 * @function aisp_identifyMainContent - 识别主要内容区域
 * @description 使用启发式算法识别页面的主要内容
 * @returns {Object} 主要内容数据
 */
function aisp_identifyMainContent() {
    const candidates = [];

    // 尝试常见的主内容选择器
    for (const selector of CONTENT_SELECTORS.main) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            if (aisp_isValidContentElement(element)) {
                candidates.push({
                    element: element,
                    score: aisp_calculateContentScore(element),
                    selector: selector
                });
            }
        });
    }

    // 如果没有找到明确的主内容，分析所有可能的容器
    if (candidates.length === 0) {
        const containers = document.querySelectorAll('div, section, article');
        containers.forEach(element => {
            const score = aisp_calculateContentScore(element);
            if (score > AISP_CONFIG.CONTENT_SCORE_THRESHOLD) {
                candidates.push({
                    element: element,
                    score: score,
                    selector: 'auto-detected'
                });
            }
        });
    }

    // 选择得分最高的元素
    candidates.sort((a, b) => b.score - a.score);
    const bestCandidate = candidates[0];

    if (bestCandidate) {
        return {
            text: aisp_extractCleanText(bestCandidate.element),
            html: bestCandidate.element.innerHTML,
            score: bestCandidate.score,
            selector: bestCandidate.selector,
            wordCount: aisp_countWords(bestCandidate.element),
            readingTime: aisp_estimateReadingTime(bestCandidate.element)
        };
    }

    return null;
}

/**
 * @function aisp_calculateContentScore - 计算内容重要性分数
 * @description 基于多个因素计算元素的内容重要性
 * @param {Element} element - 要评分的元素
 * @returns {number} 内容分数 (0-1)
 */
function aisp_calculateContentScore(element) {
    if (!element || aisp_isExcludedElement(element)) {
        return 0;
    }

    let score = 0;
    const text = aisp_extractCleanText(element);
    const wordCount = aisp_countWords(element);

    // 文本长度分数 (0-0.4)
    if (wordCount > 100) score += 0.4;
    else if (wordCount > 50) score += 0.3;
    else if (wordCount > 20) score += 0.2;
    else if (wordCount > 5) score += 0.1;

    // 段落密度分数 (0-0.2)
    const paragraphs = element.querySelectorAll('p');
    if (paragraphs.length > 3) score += 0.2;
    else if (paragraphs.length > 1) score += 0.1;

    // 标题存在分数 (0-0.2)
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
    if (headings.length > 0) score += 0.2;

    // 链接密度惩罚 (最多-0.3)
    const links = element.querySelectorAll('a');
    const linkDensity = links.length / Math.max(wordCount / 10, 1);
    if (linkDensity > 0.5) score -= 0.3;
    else if (linkDensity > 0.3) score -= 0.2;
    else if (linkDensity > 0.1) score -= 0.1;

    // 类名和ID加分
    const className = element.className.toLowerCase();
    const id = element.id.toLowerCase();
    if (className.includes('content') || className.includes('main') ||
        className.includes('article') || className.includes('post')) {
        score += 0.1;
    }
    if (id.includes('content') || id.includes('main') ||
        id.includes('article') || id.includes('post')) {
        score += 0.1;
    }

    return Math.max(0, Math.min(1, score));
}

/**
 * @function aisp_extractStructuredContent - 提取结构化内容
 * @description 提取页面中的结构化数据
 * @returns {Object} 结构化内容数据
 */
function aisp_extractStructuredContent() {
    return {
        headings: aisp_extractHeadings(),
        lists: aisp_extractLists(),
        tables: aisp_extractTables(),
        codeBlocks: aisp_extractCodeBlocks(),
        quotes: aisp_extractQuotes(),
        links: aisp_extractLinks(),
        forms: aisp_extractForms()
    };
}

/**
 * @function aisp_detectContentType - 检测内容类型
 * @description 基于页面特征检测内容类型
 * @returns {string} 内容类型
 */
function aisp_detectContentType() {
    const scores = {};

    // 计算每种内容类型的匹配分数
    for (const [type, rules] of Object.entries(CONTENT_TYPE_RULES)) {
        let score = 0;

        // 检查选择器匹配
        for (const selector of rules.selectors) {
            if (document.querySelector(selector)) {
                score += 0.3;
            }
        }

        // 检查指示器匹配
        for (const indicator of rules.indicators) {
            const elements = document.querySelectorAll(indicator);
            if (elements.length > 0) {
                score += 0.1 * Math.min(elements.length, 5);
            }
        }

        scores[type] = score * rules.weight;
    }

    // 返回得分最高的类型
    const sortedTypes = Object.entries(scores).sort((a, b) => b[1] - a[1]);
    return sortedTypes[0] ? sortedTypes[0][0] : 'unknown';
}

/**
 * @function aisp_extractMetadata - 提取页面元数据
 * @description 提取页面的元数据信息
 * @returns {Object} 元数据对象
 */
function aisp_extractMetadata() {
    const metadata = {
        title: document.title,
        description: '',
        keywords: '',
        author: '',
        publishDate: '',
        modifiedDate: '',
        canonicalUrl: '',
        ogData: {},
        twitterData: {},
        jsonLd: []
    };

    // 提取meta标签信息
    const metaTags = document.querySelectorAll('meta');
    metaTags.forEach(meta => {
        const name = meta.getAttribute('name') || meta.getAttribute('property');
        const content = meta.getAttribute('content');

        if (name && content) {
            switch (name.toLowerCase()) {
                case 'description':
                    metadata.description = content;
                    break;
                case 'keywords':
                    metadata.keywords = content;
                    break;
                case 'author':
                    metadata.author = content;
                    break;
                case 'article:published_time':
                case 'publish_date':
                    metadata.publishDate = content;
                    break;
                case 'article:modified_time':
                case 'modified_date':
                    metadata.modifiedDate = content;
                    break;
                default:
                    if (name.startsWith('og:')) {
                        metadata.ogData[name] = content;
                    } else if (name.startsWith('twitter:')) {
                        metadata.twitterData[name] = content;
                    }
            }
        }
    });

    // 提取canonical URL
    const canonical = document.querySelector('link[rel="canonical"]');
    if (canonical) {
        metadata.canonicalUrl = canonical.href;
    }

    // 提取JSON-LD结构化数据
    const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
    jsonLdScripts.forEach(script => {
        try {
            const data = JSON.parse(script.textContent);
            metadata.jsonLd.push(data);
        } catch (e) {
            // 忽略解析错误
        }
    });

    return metadata;
}

/**
 * @function aisp_extractHeadings - 提取标题层次
 * @description 提取页面的标题结构
 * @returns {Array} 标题数组
 */
function aisp_extractHeadings() {
    const headings = [];
    const headingElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6');

    headingElements.forEach((heading, index) => {
        if (aisp_isValidContentElement(heading)) {
            headings.push({
                level: parseInt(heading.tagName.charAt(1)),
                text: aisp_extractCleanText(heading),
                id: heading.id || `heading-${index}`,
                position: index
            });
        }
    });

    return headings;
}

/**
 * @function aisp_extractLists - 提取列表内容
 * @description 提取页面中的列表结构
 * @returns {Array} 列表数组
 */
function aisp_extractLists() {
    const lists = [];
    const listElements = document.querySelectorAll('ul, ol, dl');

    listElements.forEach((list, index) => {
        if (aisp_isValidContentElement(list)) {
            const items = [];
            const itemElements = list.querySelectorAll('li, dt, dd');

            itemElements.forEach(item => {
                items.push({
                    text: aisp_extractCleanText(item),
                    type: item.tagName.toLowerCase()
                });
            });

            lists.push({
                type: list.tagName.toLowerCase(),
                items: items,
                id: list.id || `list-${index}`
            });
        }
    });

    return lists;
}

/**
 * @function aisp_extractTables - 提取表格数据
 * @description 提取页面中的表格内容
 * @returns {Array} 表格数组
 */
function aisp_extractTables() {
    const tables = [];
    const tableElements = document.querySelectorAll('table');

    tableElements.forEach((table, index) => {
        if (aisp_isValidContentElement(table)) {
            const headers = [];
            const rows = [];

            // 提取表头
            const headerCells = table.querySelectorAll('th');
            headerCells.forEach(cell => {
                headers.push(aisp_extractCleanText(cell));
            });

            // 提取数据行
            const dataRows = table.querySelectorAll('tbody tr, tr');
            dataRows.forEach(row => {
                const cells = [];
                const cellElements = row.querySelectorAll('td, th');
                cellElements.forEach(cell => {
                    cells.push(aisp_extractCleanText(cell));
                });
                if (cells.length > 0) {
                    rows.push(cells);
                }
            });

            tables.push({
                headers: headers,
                rows: rows,
                id: table.id || `table-${index}`,
                caption: table.caption ? aisp_extractCleanText(table.caption) : ''
            });
        }
    });

    return tables;
}

/**
 * @function aisp_extractCodeBlocks - 提取代码块
 * @description 提取页面中的代码内容
 * @returns {Array} 代码块数组
 */
function aisp_extractCodeBlocks() {
    const codeBlocks = [];
    const codeElements = document.querySelectorAll('pre, code, .highlight, .code-block');

    codeElements.forEach((element, index) => {
        if (aisp_isValidContentElement(element)) {
            codeBlocks.push({
                text: element.textContent.trim(),
                language: aisp_detectCodeLanguage(element),
                id: element.id || `code-${index}`,
                type: element.tagName.toLowerCase()
            });
        }
    });

    return codeBlocks;
}

/**
 * @function aisp_extractQuotes - 提取引用内容
 * @description 提取页面中的引用块
 * @returns {Array} 引用数组
 */
function aisp_extractQuotes() {
    const quotes = [];
    const quoteElements = document.querySelectorAll('blockquote, .quote');

    quoteElements.forEach((element, index) => {
        if (aisp_isValidContentElement(element)) {
            quotes.push({
                text: aisp_extractCleanText(element),
                cite: element.getAttribute('cite') || '',
                id: element.id || `quote-${index}`
            });
        }
    });

    return quotes;
}

/**
 * @function aisp_extractLinks - 提取链接信息
 * @description 提取页面中的重要链接
 * @returns {Array} 链接数组
 */
function aisp_extractLinks() {
    const links = [];
    const linkElements = document.querySelectorAll('a[href]');

    linkElements.forEach((link, index) => {
        if (aisp_isValidContentElement(link) && link.href && !link.href.startsWith('javascript:')) {
            const text = aisp_extractCleanText(link);
            if (text.length > 0 && text.length < 200) {
                links.push({
                    text: text,
                    href: link.href,
                    title: link.title || '',
                    target: link.target || '',
                    id: link.id || `link-${index}`
                });
            }
        }
    });

    return links.slice(0, 50); // 限制链接数量
}

/**
 * @function aisp_extractForms - 提取表单信息
 * @description 提取页面中的表单结构
 * @returns {Array} 表单数组
 */
function aisp_extractForms() {
    const forms = [];
    const formElements = document.querySelectorAll('form');

    formElements.forEach((form, index) => {
        if (aisp_isValidContentElement(form)) {
            const fields = [];
            const fieldElements = form.querySelectorAll('input, textarea, select');

            fieldElements.forEach(field => {
                if (field.type !== 'hidden') {
                    fields.push({
                        type: field.type || field.tagName.toLowerCase(),
                        name: field.name || '',
                        placeholder: field.placeholder || '',
                        required: field.required || false,
                        label: aisp_findFieldLabel(field)
                    });
                }
            });

            forms.push({
                action: form.action || '',
                method: form.method || 'get',
                fields: fields,
                id: form.id || `form-${index}`
            });
        }
    });

    return forms;
}

// #endregion

// #region 辅助工具函数

/**
 * @function aisp_isValidContentElement - 检查元素是否为有效内容
 * @description 判断元素是否应该被包含在内容分析中
 * @param {Element} element - 要检查的元素
 * @returns {boolean} 是否为有效内容
 */
function aisp_isValidContentElement(element) {
    if (!element || !element.textContent) return false;

    // 检查元素是否可见
    const style = window.getComputedStyle(element);
    if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
        return false;
    }

    // 检查元素是否在排除列表中
    return !aisp_isExcludedElement(element);
}

/**
 * @function aisp_isExcludedElement - 检查元素是否应该被排除
 * @description 判断元素是否在排除列表中
 * @param {Element} element - 要检查的元素
 * @returns {boolean} 是否应该被排除
 */
function aisp_isExcludedElement(element) {
    for (const selector of CONTENT_SELECTORS.exclude) {
        if (element.matches && element.matches(selector)) {
            return true;
        }
        if (element.closest && element.closest(selector)) {
            return true;
        }
    }
    return false;
}

/**
 * @function aisp_extractCleanText - 提取干净的文本
 * @description 从元素中提取并清理文本内容
 * @param {Element} element - 源元素
 * @returns {string} 清理后的文本
 */
function aisp_extractCleanText(element) {
    if (!element) return '';

    // 克隆元素以避免修改原始DOM
    const clone = element.cloneNode(true);

    // 移除脚本和样式标签
    const scriptsAndStyles = clone.querySelectorAll('script, style, noscript');
    scriptsAndStyles.forEach(el => el.remove());

    // 获取文本内容并清理
    let text = clone.textContent || clone.innerText || '';

    // 清理多余的空白字符
    text = text.replace(/\s+/g, ' ').trim();

    return text;
}

/**
 * @function aisp_countWords - 计算单词数量
 * @description 计算元素中的单词数量
 * @param {Element} element - 要计算的元素
 * @returns {number} 单词数量
 */
function aisp_countWords(element) {
    const text = aisp_extractCleanText(element);
    if (!text) return 0;

    // 简单的单词计数（支持中英文）
    const words = text.match(/[\w\u4e00-\u9fff]+/g);
    return words ? words.length : 0;
}

/**
 * @function aisp_estimateReadingTime - 估算阅读时间
 * @description 基于文本长度估算阅读时间
 * @param {Element} element - 要分析的元素（可选）
 * @returns {number} 估算的阅读时间（分钟）
 */
function aisp_estimateReadingTime(element = document.body) {
    const wordCount = aisp_countWords(element);
    const wordsPerMinute = 200; // 平均阅读速度
    return Math.ceil(wordCount / wordsPerMinute);
}

/**
 * @function aisp_detectLanguage - 检测页面语言
 * @description 检测页面的主要语言
 * @returns {string} 语言代码
 */
function aisp_detectLanguage() {
    // 首先检查HTML lang属性
    const htmlLang = document.documentElement.lang;
    if (htmlLang) return htmlLang;

    // 检查meta标签
    const metaLang = document.querySelector('meta[http-equiv="content-language"]');
    if (metaLang) return metaLang.content;

    // 简单的语言检测（基于字符）
    const text = aisp_extractCleanText(document.body).substring(0, 1000);
    const chineseChars = text.match(/[\u4e00-\u9fff]/g);
    const japaneseChars = text.match(/[\u3040-\u309f\u30a0-\u30ff]/g);
    const koreanChars = text.match(/[\uac00-\ud7af]/g);

    if (chineseChars && chineseChars.length > 10) return 'zh';
    if (japaneseChars && japaneseChars.length > 5) return 'ja';
    if (koreanChars && koreanChars.length > 5) return 'ko';

    return 'en'; // 默认英语
}

/**
 * @function aisp_detectCodeLanguage - 检测代码语言
 * @description 检测代码块的编程语言
 * @param {Element} element - 代码元素
 * @returns {string} 编程语言
 */
function aisp_detectCodeLanguage(element) {
    // 检查class属性中的语言标识
    const className = element.className.toLowerCase();
    const languagePatterns = {
        'javascript': /\b(js|javascript)\b/,
        'python': /\bpython\b/,
        'java': /\bjava\b/,
        'css': /\bcss\b/,
        'html': /\bhtml\b/,
        'php': /\bphp\b/,
        'cpp': /\b(cpp|c\+\+)\b/,
        'c': /\bc\b/,
        'ruby': /\bruby\b/,
        'go': /\bgo\b/,
        'rust': /\brust\b/,
        'typescript': /\b(ts|typescript)\b/
    };

    for (const [lang, pattern] of Object.entries(languagePatterns)) {
        if (pattern.test(className)) {
            return lang;
        }
    }

    return 'unknown';
}

/**
 * @function aisp_findFieldLabel - 查找表单字段的标签
 * @description 查找与表单字段关联的标签文本
 * @param {Element} field - 表单字段元素
 * @returns {string} 标签文本
 */
function aisp_findFieldLabel(field) {
    // 检查for属性关联的label
    if (field.id) {
        const label = document.querySelector(`label[for="${field.id}"]`);
        if (label) return aisp_extractCleanText(label);
    }

    // 检查父级label
    const parentLabel = field.closest('label');
    if (parentLabel) return aisp_extractCleanText(parentLabel);

    // 检查前面的兄弟元素
    let sibling = field.previousElementSibling;
    while (sibling) {
        if (sibling.tagName === 'LABEL') {
            return aisp_extractCleanText(sibling);
        }
        sibling = sibling.previousElementSibling;
    }

    return '';
}

// #endregion

// #region 性能优化和缓存管理

/**
 * @function aisp_shouldRecapture - 判断是否需要重新捕获
 * @description 基于时间和内容变化判断是否需要重新捕获
 * @returns {boolean} 是否需要重新捕获
 */
function aisp_shouldRecapture() {
    const now = Date.now();
    const timeSinceLastCapture = now - aisp_lastCaptureTime;

    // 如果距离上次捕获时间太短，跳过
    if (timeSinceLastCapture < AISP_CONFIG.CONTENT_CAPTURE_DELAY) {
        return false;
    }

    aisp_lastCaptureTime = now;
    return true;
}

/**
 * @function aisp_cacheContentData - 缓存内容数据
 * @description 将内容数据存储到缓存中
 * @param {Object} contentData - 内容数据
 */
function aisp_cacheContentData(contentData) {
    const cacheKey = contentData.url + '_' + contentData.timestamp;
    aisp_contentCache.set(cacheKey, contentData);

    // 限制缓存大小
    if (aisp_contentCache.size > 10) {
        const firstKey = aisp_contentCache.keys().next().value;
        aisp_contentCache.delete(firstKey);
    }
}

/**
 * @function aisp_generateCaptureId - 生成捕获ID
 * @description 生成唯一的捕获标识符
 * @returns {string} 捕获ID
 */
function aisp_generateCaptureId() {
    return `capture_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * @function aisp_updatePerformanceMetrics - 更新性能指标
 * @description 更新内容捕获的性能统计
 * @param {number} startTime - 开始时间
 */
function aisp_updatePerformanceMetrics(startTime) {
    const duration = performance.now() - startTime;
    aisp_performanceMetrics.captureCount++;
    aisp_performanceMetrics.lastCaptureTime = duration;

    // 计算平均时间
    const totalTime = aisp_performanceMetrics.averageTime * (aisp_performanceMetrics.captureCount - 1) + duration;
    aisp_performanceMetrics.averageTime = totalTime / aisp_performanceMetrics.captureCount;
}

// #endregion

// #region 事件处理和监听器

/**
 * @function aisp_setupIntersectionObserver - 设置可见区域观察器
 * @description 监听元素进入和离开可视区域
 */
function aisp_setupIntersectionObserver() {
    if (!window.IntersectionObserver) return;

    aisp_intersectionObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // 元素进入可视区域
                aisp_handleElementVisible(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '50px'
    });

    // 观察主要内容区域
    const mainElements = document.querySelectorAll(CONTENT_SELECTORS.main.join(', '));
    mainElements.forEach(element => {
        aisp_intersectionObserver.observe(element);
    });
}

/**
 * @function aisp_handleContentChange - 处理内容变化
 * @description 智能处理页面内容变化
 * @param {Array} mutations - 变化记录
 */
function aisp_handleContentChange(mutations) {
    let significantChange = false;

    mutations.forEach(mutation => {
        // 检查是否为重要变化
        if (aisp_isSignificantChange(mutation)) {
            significantChange = true;
        }
    });

    if (significantChange) {
        aisp_capturePageContent();
    }
}

/**
 * @function aisp_isSignificantChange - 判断是否为重要变化
 * @description 判断DOM变化是否足够重要需要重新捕获
 * @param {MutationRecord} mutation - 变化记录
 * @returns {boolean} 是否为重要变化
 */
function aisp_isSignificantChange(mutation) {
    // 忽略样式和类名变化
    if (mutation.type === 'attributes' &&
        ['style', 'class'].includes(mutation.attributeName)) {
        return false;
    }

    // 检查添加或删除的节点
    if (mutation.type === 'childList') {
        const addedNodes = Array.from(mutation.addedNodes);
        const removedNodes = Array.from(mutation.removedNodes);

        // 检查是否有重要的内容节点变化
        const importantNodes = [...addedNodes, ...removedNodes].filter(node => {
            return node.nodeType === Node.ELEMENT_NODE &&
                   aisp_isImportantElement(node);
        });

        return importantNodes.length > 0;
    }

    // 文本内容变化
    if (mutation.type === 'characterData') {
        const parent = mutation.target.parentElement;
        return parent && aisp_isImportantElement(parent);
    }

    return false;
}

/**
 * @function aisp_isImportantElement - 判断元素是否重要
 * @description 判断元素是否对内容分析重要
 * @param {Element} element - 要检查的元素
 * @returns {boolean} 是否重要
 */
function aisp_isImportantElement(element) {
    if (!element || element.nodeType !== Node.ELEMENT_NODE) return false;

    const tagName = element.tagName.toLowerCase();
    const importantTags = ['article', 'main', 'section', 'div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'];

    return importantTags.includes(tagName) &&
           !aisp_isExcludedElement(element) &&
           aisp_extractCleanText(element).length > 10;
}

/**
 * @function aisp_handleElementVisible - 处理元素可见
 * @description 当元素进入可视区域时的处理
 * @param {Element} element - 可见的元素
 */
function aisp_handleElementVisible(element) {
    // 可以在这里添加懒加载内容的处理
    console.log('元素进入可视区域:', element);
}

/**
 * @function aisp_handleViewportChange - 处理视口变化
 * @description 处理页面滚动和窗口大小变化
 */
function aisp_handleViewportChange() {
    // 可以在这里添加视口变化的处理逻辑
    // 例如：检测新的可见内容、更新内容优先级等
}

// #endregion

// #region 错误处理和恢复

/**
 * @function aisp_setupErrorHandling - 设置错误处理
 * @description 设置全局错误处理机制
 */
function aisp_setupErrorHandling() {
    window.addEventListener('error', aisp_handleGlobalError);
    window.addEventListener('unhandledrejection', aisp_handleUnhandledRejection);
}

/**
 * @function aisp_handleInitializationError - 处理初始化错误
 * @description 处理初始化过程中的错误
 * @param {Error} error - 错误对象
 */
function aisp_handleInitializationError(error) {
    console.error('AI Side Panel 初始化错误:', error);

    // 尝试重新初始化
    setTimeout(() => {
        if (!aisp_isInitialized) {
            console.log('尝试重新初始化...');
            aisp_initializeContentScript();
        }
    }, 5000);
}

/**
 * @function aisp_handleCaptureError - 处理捕获错误
 * @description 处理内容捕获过程中的错误
 * @param {Error} error - 错误对象
 */
function aisp_handleCaptureError(error) {
    console.error('内容捕获错误:', error);

    // 记录错误信息
    try {
        chrome.runtime.sendMessage({
            action: 'capture_error',
            error: {
                message: error.message,
                stack: error.stack,
                url: window.location.href,
                timestamp: Date.now()
            }
        });
    } catch (e) {
        // 忽略发送错误
    }
}

/**
 * @function aisp_handleGlobalError - 处理全局错误
 * @description 处理全局JavaScript错误
 * @param {ErrorEvent} event - 错误事件
 */
function aisp_handleGlobalError(event) {
    if (event.filename && event.filename.includes('content-script.js')) {
        console.error('AI Side Panel 全局错误:', event.error);
        aisp_performanceMetrics.errorCount++;
    }
}

/**
 * @function aisp_handleUnhandledRejection - 处理未捕获的Promise拒绝
 * @description 处理未捕获的Promise错误
 * @param {PromiseRejectionEvent} event - Promise拒绝事件
 */
function aisp_handleUnhandledRejection(event) {
    console.error('AI Side Panel Promise拒绝:', event.reason);
    aisp_performanceMetrics.errorCount++;
}

// #endregion

// #region 性能监控

/**
 * @function aisp_setupPerformanceMonitoring - 设置性能监控
 * @description 设置性能监控和报告
 */
function aisp_setupPerformanceMonitoring() {
    // 定期报告性能指标
    setInterval(() => {
        if (aisp_performanceMetrics.captureCount > 0) {
            console.log('AI Side Panel 性能指标:', aisp_performanceMetrics);
        }
    }, 60000); // 每分钟报告一次
}

// #endregion

// #region 页面类型检测辅助函数

/**
 * @function aisp_identifyContentAreas - 识别内容区域
 * @description 识别页面中的不同内容区域
 * @returns {Object} 内容区域信息
 */
function aisp_identifyContentAreas() {
    return {
        header: document.querySelector('header, .header, #header'),
        navigation: document.querySelector('nav, .nav, .navigation'),
        main: document.querySelector('main, .main, .content'),
        sidebar: document.querySelector('aside, .sidebar, .side'),
        footer: document.querySelector('footer, .footer, #footer')
    };
}

/**
 * @function aisp_guessPageType - 猜测页面类型
 * @description 基于URL和内容特征猜测页面类型
 * @returns {string} 页面类型
 */
function aisp_guessPageType() {
    const url = window.location.href.toLowerCase();
    const pathname = window.location.pathname.toLowerCase();

    // 基于URL模式判断
    if (pathname.includes('/article/') || pathname.includes('/post/') || pathname.includes('/blog/')) {
        return 'article';
    }
    if (pathname.includes('/product/') || pathname.includes('/item/') || url.includes('shop')) {
        return 'product';
    }
    if (pathname.includes('/search') || url.includes('search=')) {
        return 'search';
    }
    if (pathname.includes('/contact') || pathname.includes('/form')) {
        return 'form';
    }
    if (pathname === '/' || pathname === '/index.html') {
        return 'homepage';
    }

    return 'unknown';
}

/**
 * @function aisp_isValidImageElement - 检查图片元素是否有效
 * @description 判断图片是否应该被包含在分析中
 * @param {Element} img - 图片元素
 * @returns {boolean} 是否为有效图片
 */
function aisp_isValidImageElement(img) {
    if (!img || !img.src) return false;

    // 检查图片尺寸
    const width = img.offsetWidth || img.naturalWidth || 0;
    const height = img.offsetHeight || img.naturalHeight || 0;

    if (width < AISP_CONFIG.MIN_IMAGE_SIZE || height < AISP_CONFIG.MIN_IMAGE_SIZE) {
        return false;
    }

    // 检查图片是否可见
    const style = window.getComputedStyle(img);
    if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
        return false;
    }

    // 排除装饰性图片
    const src = img.src.toLowerCase();
    const decorativePatterns = [
        'icon', 'logo', 'avatar', 'thumbnail', 'badge', 'button',
        'arrow', 'bullet', 'separator', 'spacer', 'pixel'
    ];

    for (const pattern of decorativePatterns) {
        if (src.includes(pattern) && (width < 100 || height < 100)) {
            return false;
        }
    }

    return true;
}

/**
 * @function aisp_calculateImageImportance - 计算图片重要性
 * @description 基于多个因素计算图片的重要性分数
 * @param {Element} img - 图片元素
 * @returns {number} 重要性分数 (0-1)
 */
function aisp_calculateImageImportance(img) {
    let score = 0;

    const width = img.offsetWidth || img.naturalWidth || 0;
    const height = img.offsetHeight || img.naturalHeight || 0;
    const area = width * height;

    // 尺寸分数 (0-0.4)
    if (area > 100000) score += 0.4;      // 大图片
    else if (area > 50000) score += 0.3;  // 中等图片
    else if (area > 10000) score += 0.2;  // 小图片
    else score += 0.1;                    // 很小的图片

    // 位置分数 (0-0.3)
    const rect = img.getBoundingClientRect();
    const viewportHeight = window.innerHeight;

    if (rect.top < viewportHeight) {
        score += 0.3; // 在首屏可见
    } else if (rect.top < viewportHeight * 2) {
        score += 0.2; // 在第二屏
    } else {
        score += 0.1; // 更下方
    }

    // Alt文本分数 (0-0.2)
    if (img.alt && img.alt.length > 5) {
        score += 0.2;
    } else if (img.alt) {
        score += 0.1;
    }

    // 父元素重要性 (0-0.1)
    const parent = img.closest('article, main, .content, .post');
    if (parent) {
        score += 0.1;
    }

    return Math.max(0, Math.min(1, score));
}

console.log('AI Side Panel Content Script 已加载');
// #endregion

// #region 消息监听
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'show_template_popup') {
        aisp_showTemplatePopup(request.data);
        sendResponse({ success: true });
    } else if (request.action === 'get_page_content') {
        // 处理获取页面内容的请求
        aisp_handleGetPageContentRequest(sendResponse);
        return true; // 异步响应
    }

    return true; // 保持消息通道开放
});

/**
 * @function aisp_handleGetPageContentRequest - 处理获取页面内容请求
 * @description 响应侧边栏请求页面内容的消息
 * @param {Function} sendResponse - 响应函数
 */
async function aisp_handleGetPageContentRequest(sendResponse) {
    try {
        // 重新捕获最新的页面内容
        await aisp_capturePageContent();

        // 准备返回的内容数据
        const contentData = {
            text: aisp_extractText(),
            url: window.location.href,
            title: document.title,
            structuredContent: aisp_extractStructuredContent(),
            metadata: aisp_extractMetadata(),
            timestamp: Date.now()
        };

        sendResponse({
            success: true,
            data: contentData
        });

    } catch (error) {
        console.error('获取页面内容失败:', error);
        sendResponse({
            success: false,
            error: error.message || '获取页面内容失败'
        });
    }
}
// #endregion
