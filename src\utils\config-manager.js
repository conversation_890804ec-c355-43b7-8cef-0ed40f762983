/**
 * @file 配置管理模块
 * @description 管理扩展的配置信息，包括API密钥、用户设置等
 */

// #region 配置常量

/**
 * 配置键名常量
 */
const CONFIG_KEYS = {
    // 注意：GEMINI_API_KEY 已移除，现在使用硬编码方式
    USER_LANGUAGE: 'user_language',
    THEME_MODE: 'theme_mode',
    AUTO_ANALYZE: 'auto_analyze',
    CACHE_ENABLED: 'cache_enabled',
    NOTIFICATION_ENABLED: 'notification_enabled',
    REPLY_STYLE: 'reply_style',
    TEMPLATE_SETTINGS: 'template_settings',
    DRIVE_SETTINGS: 'drive_settings',
    PERFORMANCE_SETTINGS: 'performance_settings'
};

/**
 * 默认配置
 */
const DEFAULT_CONFIG = {
    [CONFIG_KEYS.USER_LANGUAGE]: 'zh_CN',
    [CONFIG_KEYS.THEME_MODE]: 'auto',
    [CONFIG_KEYS.AUTO_ANALYZE]: true,
    [CONFIG_KEYS.CACHE_ENABLED]: true,
    [CONFIG_KEYS.NOTIFICATION_ENABLED]: true,
    [CONFIG_KEYS.REPLY_STYLE]: 'professional',
    [CONFIG_KEYS.TEMPLATE_SETTINGS]: {
        autoSuggest: true,
        maxSuggestions: 5,
        showPreview: true
    },
    [CONFIG_KEYS.DRIVE_SETTINGS]: {
        autoSync: false,
        syncInterval: 300000, // 5分钟
        maxFileSize: 10485760 // 10MB
    },
    [CONFIG_KEYS.PERFORMANCE_SETTINGS]: {
        maxCacheSize: 150,        // 增加缓存大小
        requestTimeout: 45000,    // 增加超时时间适应Gemini 2.0
        retryAttempts: 3,
        maxInputLength: 100000,   // Gemini 2.0 Flash支持更大输入
        maxOutputLength: 8000,    // 增加输出长度
        rateLimit: 100           // 每分钟请求限制
    }
};

// #endregion

// #region 配置管理类

/**
 * @class ConfigManager - 配置管理器
 * @description 管理扩展的所有配置信息
 */
class ConfigManager {
    /**
     * @constructor
     */
    constructor() {
        this.cache = new Map();
        this.listeners = new Map();
        this.initialized = false;
        
        console.log('配置管理器初始化');
    }

    /**
     * @function initialize - 初始化配置管理器
     * @description 加载所有配置并设置默认值
     * @returns {Promise<void>}
     */
    async initialize() {
        if (this.initialized) return;
        
        try {
            // 加载所有配置
            await this.loadAllConfigs();
            
            // 设置默认值
            await this.setDefaultValues();
            
            this.initialized = true;
            console.log('配置管理器初始化完成');
            
        } catch (error) {
            console.error('配置管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * @function get - 获取配置值
     * @description 获取指定键的配置值
     * @param {string} key - 配置键
     * @param {*} defaultValue - 默认值
     * @returns {Promise<*>} 配置值
     */
    async get(key, defaultValue = null) {
        try {
            // 先从缓存获取
            if (this.cache.has(key)) {
                return this.cache.get(key);
            }
            
            // 从存储获取
            const result = await chrome.storage.sync.get(key);
            const value = result[key] !== undefined ? result[key] : defaultValue;
            
            // 缓存结果
            this.cache.set(key, value);
            
            return value;
            
        } catch (error) {
            console.error(`获取配置失败 [${key}]:`, error);
            return defaultValue;
        }
    }

    /**
     * @function set - 设置配置值
     * @description 设置指定键的配置值
     * @param {string} key - 配置键
     * @param {*} value - 配置值
     * @returns {Promise<void>}
     */
    async set(key, value) {
        try {
            // 保存到存储
            await chrome.storage.sync.set({ [key]: value });
            
            // 更新缓存
            this.cache.set(key, value);
            
            // 触发监听器
            this.notifyListeners(key, value);
            
            console.log(`配置已更新 [${key}]:`, value);
            
        } catch (error) {
            console.error(`设置配置失败 [${key}]:`, error);
            throw error;
        }
    }

    /**
     * @function getMultiple - 批量获取配置
     * @description 批量获取多个配置值
     * @param {Array<string>} keys - 配置键数组
     * @returns {Promise<Object>} 配置对象
     */
    async getMultiple(keys) {
        try {
            const result = {};
            
            for (const key of keys) {
                result[key] = await this.get(key);
            }
            
            return result;
            
        } catch (error) {
            console.error('批量获取配置失败:', error);
            throw error;
        }
    }

    /**
     * @function setMultiple - 批量设置配置
     * @description 批量设置多个配置值
     * @param {Object} configs - 配置对象
     * @returns {Promise<void>}
     */
    async setMultiple(configs) {
        try {
            // 保存到存储
            await chrome.storage.sync.set(configs);
            
            // 更新缓存并触发监听器
            for (const [key, value] of Object.entries(configs)) {
                this.cache.set(key, value);
                this.notifyListeners(key, value);
            }
            
            console.log('批量配置已更新:', Object.keys(configs));
            
        } catch (error) {
            console.error('批量设置配置失败:', error);
            throw error;
        }
    }

    /**
     * @function remove - 删除配置
     * @description 删除指定的配置项
     * @param {string} key - 配置键
     * @returns {Promise<void>}
     */
    async remove(key) {
        try {
            await chrome.storage.sync.remove(key);
            this.cache.delete(key);
            this.notifyListeners(key, null);
            
            console.log(`配置已删除 [${key}]`);
            
        } catch (error) {
            console.error(`删除配置失败 [${key}]:`, error);
            throw error;
        }
    }

    /**
     * @function clear - 清空所有配置
     * @description 清空所有配置数据
     * @returns {Promise<void>}
     */
    async clear() {
        try {
            await chrome.storage.sync.clear();
            this.cache.clear();
            
            console.log('所有配置已清空');
            
        } catch (error) {
            console.error('清空配置失败:', error);
            throw error;
        }
    }

    /**
     * @function addListener - 添加配置变化监听器
     * @description 监听指定配置的变化
     * @param {string} key - 配置键
     * @param {Function} callback - 回调函数
     */
    addListener(key, callback) {
        if (!this.listeners.has(key)) {
            this.listeners.set(key, new Set());
        }
        
        this.listeners.get(key).add(callback);
        console.log(`已添加配置监听器 [${key}]`);
    }

    /**
     * @function removeListener - 移除配置变化监听器
     * @description 移除指定配置的监听器
     * @param {string} key - 配置键
     * @param {Function} callback - 回调函数
     */
    removeListener(key, callback) {
        if (this.listeners.has(key)) {
            this.listeners.get(key).delete(callback);
            console.log(`已移除配置监听器 [${key}]`);
        }
    }

    /**
     * @function notifyListeners - 通知监听器
     * @description 通知指定配置的所有监听器
     * @param {string} key - 配置键
     * @param {*} value - 新值
     * @private
     */
    notifyListeners(key, value) {
        if (this.listeners.has(key)) {
            this.listeners.get(key).forEach(callback => {
                try {
                    callback(value, key);
                } catch (error) {
                    console.error(`配置监听器执行失败 [${key}]:`, error);
                }
            });
        }
    }

    /**
     * @function loadAllConfigs - 加载所有配置
     * @description 从存储中加载所有配置到缓存
     * @returns {Promise<void>}
     * @private
     */
    async loadAllConfigs() {
        try {
            const allConfigs = await chrome.storage.sync.get(null);
            
            for (const [key, value] of Object.entries(allConfigs)) {
                this.cache.set(key, value);
            }
            
            console.log('所有配置已加载到缓存');
            
        } catch (error) {
            console.error('加载配置失败:', error);
            throw error;
        }
    }

    /**
     * @function setDefaultValues - 设置默认值
     * @description 为未设置的配置项设置默认值
     * @returns {Promise<void>}
     * @private
     */
    async setDefaultValues() {
        try {
            const updates = {};
            
            for (const [key, defaultValue] of Object.entries(DEFAULT_CONFIG)) {
                if (!this.cache.has(key)) {
                    updates[key] = defaultValue;
                    this.cache.set(key, defaultValue);
                }
            }
            
            if (Object.keys(updates).length > 0) {
                await chrome.storage.sync.set(updates);
                console.log('默认配置已设置:', Object.keys(updates));
            }
            
        } catch (error) {
            console.error('设置默认配置失败:', error);
            throw error;
        }
    }

    // #region API密钥管理（已移除）

    /**
     * 注意：API密钥管理功能已移除
     * 现在使用硬编码方式管理API密钥，请在 src/config/api-keys.js 中配置
     *
     * @function getApiKeyInfo - 获取API密钥信息
     * @description 获取硬编码API密钥的状态信息
     * @returns {Object} API密钥状态
     */
    getApiKeyInfo() {
        // 引用硬编码的API密钥状态检查函数
        if (typeof getApiKeyStatus === 'function') {
            return getApiKeyStatus();
        } else {
            return {
                gemini: {
                    available: false,
                    status: '请确保已加载 src/config/api-keys.js 文件'
                }
            };
        }
    }

    // #endregion
}

// #endregion

// #region 单例实例

/**
 * 配置管理器单例实例
 */
let configManagerInstance = null;

/**
 * @function getConfigManager - 获取配置管理器实例
 * @description 获取配置管理器的单例实例
 * @returns {ConfigManager} 配置管理器实例
 */
function getConfigManager() {
    if (!configManagerInstance) {
        configManagerInstance = new ConfigManager();
    }
    return configManagerInstance;
}

// #endregion

// #region 导出

// 导出
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        ConfigManager,
        getConfigManager,
        CONFIG_KEYS,
        DEFAULT_CONFIG
    };
} else if (typeof window !== 'undefined') {
    // 浏览器环境
    window.ConfigManager = ConfigManager;
    window.getConfigManager = getConfigManager;
    window.CONFIG_KEYS = CONFIG_KEYS;
    window.DEFAULT_CONFIG = DEFAULT_CONFIG;
}

// #endregion

console.log('配置管理模块已加载');
