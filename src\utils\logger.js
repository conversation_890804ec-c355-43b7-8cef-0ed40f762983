/**
 * @file AI侧边栏扩展 - 统一日志管理系统
 * @description 提供全局的日志记录、错误处理和调试功能
 * <AUTHOR> Side Panel Team
 */

/**
 * 日志级别枚举
 */
const AISP_LOG_LEVELS = {
    ERROR: 0,   // 错误级别
    WARN: 1,    // 警告级别
    INFO: 2,    // 信息级别
    DEBUG: 3    // 调试级别
};

/**
 * 日志级别名称映射
 */
const AISP_LOG_LEVEL_NAMES = {
    0: 'ERROR',
    1: 'WARN',
    2: 'INFO',
    3: 'DEBUG'
};

/**
 * 日志级别颜色映射（用于控制台输出）
 */
const AISP_LOG_COLORS = {
    ERROR: '#FF4444',   // 红色
    WARN: '#FF8800',    // 橙色
    INFO: '#0088FF',    // 蓝色
    DEBUG: '#888888'    // 灰色
};

/**
 * 全局日志配置
 */
let aispLogConfig = {
    level: AISP_LOG_LEVELS.INFO,           // 默认日志级别
    enableConsole: true,                   // 启用控制台输出
    enableStorage: true,                   // 启用本地存储
    maxStorageEntries: 1000,              // 最大存储条目数
    enableTimestamp: true,                 // 启用时间戳
    enableStackTrace: true,                // 启用堆栈跟踪
    contextPrefix: 'AISP'                  // 上下文前缀
};

/**
 * @function aisp_logSetConfig - 设置日志配置
 * @param {Object} config - 日志配置对象
 * @description 更新全局日志配置
 */
function aisp_logSetConfig(config) {
    aispLogConfig = { ...aispLogConfig, ...config };
    aisp_logDebug('日志配置已更新', { config: aispLogConfig });
}

/**
 * @function aisp_logGetConfig - 获取当前日志配置
 * @returns {Object} 当前日志配置
 * @description 返回当前的日志配置
 */
function aisp_logGetConfig() {
    return { ...aispLogConfig };
}

/**
 * @function aisp_logGetContext - 获取当前执行上下文信息
 * @returns {Object} 上下文信息对象
 * @description 检测当前代码运行的Chrome扩展上下文
 */
function aisp_logGetContext() {
    let context = 'unknown';
    let contextDetails = {};

    try {
        // 检测是否在Service Worker中
        if (typeof importScripts === 'function' && typeof chrome !== 'undefined' && chrome.runtime) {
            context = 'background';
            contextDetails.type = 'service-worker';
        }
        // 检测是否在Content Script中
        else if (typeof window !== 'undefined' && window.location && window.location.href !== 'chrome-extension://') {
            context = 'content';
            contextDetails.url = window.location.href;
            contextDetails.domain = window.location.hostname;
        }
        // 检测是否在扩展页面中（sidepanel、popup等）
        else if (typeof window !== 'undefined' && window.location && window.location.href.startsWith('chrome-extension://')) {
            context = 'extension';
            contextDetails.page = window.location.pathname;
        }
        // 检测是否在DevTools中
        else if (typeof chrome !== 'undefined' && chrome.devtools) {
            context = 'devtools';
        }
    } catch (error) {
        context = 'error';
        contextDetails.error = error.message;
    }

    return {
        context,
        timestamp: new Date().toISOString(),
        ...contextDetails
    };
}

/**
 * @function aisp_logFormatMessage - 格式化日志消息
 * @param {string} level - 日志级别
 * @param {string} message - 日志消息
 * @param {Object} data - 附加数据
 * @param {Object} metadata - 元数据
 * @returns {Object} 格式化后的日志对象
 * @description 创建结构化的日志消息对象
 */
function aisp_logFormatMessage(level, message, data = null, metadata = {}) {
    const contextInfo = aisp_logGetContext();
    const timestamp = new Date().toISOString();
    
    // 获取调用堆栈信息
    let stackTrace = null;
    if (aispLogConfig.enableStackTrace && (level === 'ERROR' || level === 'WARN')) {
        try {
            throw new Error();
        } catch (e) {
            stackTrace = e.stack;
        }
    }

    // 构建日志对象
    const logEntry = {
        timestamp,
        level,
        context: contextInfo.context,
        prefix: aispLogConfig.contextPrefix,
        message,
        data,
        metadata: {
            ...metadata,
            contextDetails: contextInfo,
            stackTrace
        }
    };

    return logEntry;
}

/**
 * @function aisp_logToConsole - 输出日志到控制台
 * @param {Object} logEntry - 日志条目对象
 * @description 将格式化的日志输出到浏览器控制台
 */
function aisp_logToConsole(logEntry) {
    if (!aispLogConfig.enableConsole) return;

    const { timestamp, level, context, prefix, message, data, metadata } = logEntry;
    const color = AISP_LOG_COLORS[level];
    
    // 构建控制台输出格式
    const timeStr = aispLogConfig.enableTimestamp ? `[${timestamp.split('T')[1].split('.')[0]}]` : '';
    const contextStr = `[${prefix}-${context.toUpperCase()}]`;
    const levelStr = `[${level}]`;
    
    const fullMessage = `${timeStr} ${contextStr} ${levelStr} ${message}`;

    // 根据级别选择控制台方法
    const consoleMethod = {
        'ERROR': 'error',
        'WARN': 'warn',
        'INFO': 'info',
        'DEBUG': 'log'
    }[level] || 'log';

    // 输出到控制台
    if (data || metadata.stackTrace) {
        console[consoleMethod](
            `%c${fullMessage}`,
            `color: ${color}; font-weight: bold;`,
            data ? { data } : '',
            metadata.stackTrace ? { stack: metadata.stackTrace } : ''
        );
    } else {
        console[consoleMethod](`%c${fullMessage}`, `color: ${color}; font-weight: bold;`);
    }
}

/**
 * @function aisp_logToStorage - 保存日志到本地存储
 * @param {Object} logEntry - 日志条目对象
 * @description 将日志保存到Chrome扩展的本地存储中
 */
async function aisp_logToStorage(logEntry) {
    if (!aispLogConfig.enableStorage) return;

    try {
        // 获取现有日志
        const result = await chrome.storage.local.get(['aisp_logs']);
        let logs = result.aisp_logs || [];

        // 添加新日志
        logs.push(logEntry);

        // 限制日志数量
        if (logs.length > aispLogConfig.maxStorageEntries) {
            logs = logs.slice(-aispLogConfig.maxStorageEntries);
        }

        // 保存到存储
        await chrome.storage.local.set({ aisp_logs: logs });
    } catch (error) {
        console.error('保存日志到存储失败:', error);
    }
}

/**
 * @function aisp_logWrite - 核心日志写入函数
 * @param {string} level - 日志级别
 * @param {string} message - 日志消息
 * @param {Object} data - 附加数据
 * @param {Object} metadata - 元数据
 * @description 执行实际的日志写入操作
 */
async function aisp_logWrite(level, message, data = null, metadata = {}) {
    // 检查日志级别
    const levelValue = AISP_LOG_LEVELS[level];
    if (levelValue > aispLogConfig.level) {
        return; // 跳过低优先级日志
    }

    // 格式化日志消息
    const logEntry = aisp_logFormatMessage(level, message, data, metadata);

    // 输出到控制台
    aisp_logToConsole(logEntry);

    // 保存到存储
    await aisp_logToStorage(logEntry);
}

/**
 * @function aisp_logError - 记录错误级别日志
 * @param {string} message - 错误消息
 * @param {Object} data - 错误数据
 * @param {Object} metadata - 元数据
 * @description 记录错误级别的日志信息
 */
async function aisp_logError(message, data = null, metadata = {}) {
    await aisp_logWrite('ERROR', message, data, metadata);
}

/**
 * @function aisp_logWarn - 记录警告级别日志
 * @param {string} message - 警告消息
 * @param {Object} data - 警告数据
 * @param {Object} metadata - 元数据
 * @description 记录警告级别的日志信息
 */
async function aisp_logWarn(message, data = null, metadata = {}) {
    await aisp_logWrite('WARN', message, data, metadata);
}

/**
 * @function aisp_logInfo - 记录信息级别日志
 * @param {string} message - 信息消息
 * @param {Object} data - 信息数据
 * @param {Object} metadata - 元数据
 * @description 记录信息级别的日志信息
 */
async function aisp_logInfo(message, data = null, metadata = {}) {
    await aisp_logWrite('INFO', message, data, metadata);
}

/**
 * @function aisp_logDebug - 记录调试级别日志
 * @param {string} message - 调试消息
 * @param {Object} data - 调试数据
 * @param {Object} metadata - 元数据
 * @description 记录调试级别的日志信息
 */
async function aisp_logDebug(message, data = null, metadata = {}) {
    await aisp_logWrite('DEBUG', message, data, metadata);
}

/**
 * @function aisp_logGetStoredLogs - 获取存储的日志
 * @param {Object} filter - 过滤条件
 * @returns {Array} 日志数组
 * @description 从本地存储中获取日志记录
 */
async function aisp_logGetStoredLogs(filter = {}) {
    try {
        const result = await chrome.storage.local.get(['aisp_logs']);
        let logs = result.aisp_logs || [];

        // 应用过滤条件
        if (filter.level) {
            logs = logs.filter(log => log.level === filter.level);
        }
        if (filter.context) {
            logs = logs.filter(log => log.context === filter.context);
        }
        if (filter.since) {
            const sinceDate = new Date(filter.since);
            logs = logs.filter(log => new Date(log.timestamp) >= sinceDate);
        }

        return logs;
    } catch (error) {
        console.error('获取存储日志失败:', error);
        return [];
    }
}

/**
 * @function aisp_logClearStoredLogs - 清理存储的日志
 * @description 清空本地存储中的所有日志
 */
async function aisp_logClearStoredLogs() {
    try {
        await chrome.storage.local.remove(['aisp_logs']);
        aisp_logInfo('已清理存储的日志');
    } catch (error) {
        aisp_logError('清理存储日志失败', { error: error.message });
    }
}

/**
 * @function aisp_logExportLogs - 导出日志
 * @param {Object} options - 导出选项
 * @returns {string} 导出的日志字符串
 * @description 将日志导出为JSON或文本格式
 */
async function aisp_logExportLogs(options = {}) {
    const { format = 'json', filter = {} } = options;

    try {
        const logs = await aisp_logGetStoredLogs(filter);

        if (format === 'json') {
            return JSON.stringify(logs, null, 2);
        } else if (format === 'text') {
            return logs.map(log => {
                const timeStr = log.timestamp.split('T')[1].split('.')[0];
                return `[${timeStr}] [${log.context.toUpperCase()}] [${log.level}] ${log.message}`;
            }).join('\n');
        }

        return JSON.stringify(logs);
    } catch (error) {
        aisp_logError('导出日志失败', { error: error.message });
        return '';
    }
}

/**
 * @function aisp_logPerformance - 记录性能日志
 * @param {string} operation - 操作名称
 * @param {number} duration - 执行时间（毫秒）
 * @param {Object} data - 附加数据
 * @description 记录性能相关的日志信息
 */
async function aisp_logPerformance(operation, duration, data = {}) {
    const performanceData = {
        operation,
        duration,
        timestamp: Date.now(),
        ...data
    };

    const level = duration > 1000 ? 'WARN' : 'INFO';
    const message = `性能监控: ${operation} 耗时 ${duration}ms`;

    await aisp_logWrite(level, message, performanceData, { type: 'performance' });
}

/**
 * @function aisp_logAPICall - 记录API调用日志
 * @param {string} apiName - API名称
 * @param {string} method - HTTP方法
 * @param {string} url - 请求URL
 * @param {Object} options - 调用选项
 * @description 记录API调用的详细信息
 */
async function aisp_logAPICall(apiName, method, url, options = {}) {
    const apiData = {
        apiName,
        method,
        url,
        timestamp: Date.now(),
        ...options
    };

    await aisp_logInfo(`API调用: ${apiName} ${method} ${url}`, apiData, { type: 'api' });
}

/**
 * @function aisp_logUserAction - 记录用户操作日志
 * @param {string} action - 操作类型
 * @param {Object} details - 操作详情
 * @description 记录用户交互操作
 */
async function aisp_logUserAction(action, details = {}) {
    const actionData = {
        action,
        timestamp: Date.now(),
        ...details
    };

    await aisp_logInfo(`用户操作: ${action}`, actionData, { type: 'user_action' });
}

/**
 * @function aisp_logFunctionEntry - 记录函数进入日志
 * @param {string} functionName - 函数名称
 * @param {Object} params - 函数参数
 * @description 记录函数调用的入口点
 */
async function aisp_logFunctionEntry(functionName, params = {}) {
    await aisp_logDebug(`进入函数: ${functionName}`, { params }, { type: 'function_entry' });
}

/**
 * @function aisp_logFunctionExit - 记录函数退出日志
 * @param {string} functionName - 函数名称
 * @param {Object} result - 函数返回值
 * @description 记录函数调用的退出点
 */
async function aisp_logFunctionExit(functionName, result = {}) {
    await aisp_logDebug(`退出函数: ${functionName}`, { result }, { type: 'function_exit' });
}

/**
 * @function aisp_logWithTimer - 带计时器的日志记录
 * @param {string} operation - 操作名称
 * @param {Function} fn - 要执行的函数
 * @returns {*} 函数执行结果
 * @description 自动记录函数执行时间
 */
async function aisp_logWithTimer(operation, fn) {
    const startTime = Date.now();
    aisp_logDebug(`开始执行: ${operation}`);

    try {
        const result = await fn();
        const duration = Date.now() - startTime;
        await aisp_logPerformance(operation, duration, { success: true });
        return result;
    } catch (error) {
        const duration = Date.now() - startTime;
        await aisp_logError(`执行失败: ${operation}`, {
            error: error.message,
            duration
        });
        throw error;
    }
}

/**
 * @function aisp_logCreateLogger - 创建带上下文的日志记录器
 * @param {string} context - 上下文名称
 * @returns {Object} 日志记录器对象
 * @description 创建一个带有特定上下文的日志记录器
 */
function aisp_logCreateLogger(context) {
    return {
        error: (message, data, metadata = {}) =>
            aisp_logError(message, data, { ...metadata, context }),
        warn: (message, data, metadata = {}) =>
            aisp_logWarn(message, data, { ...metadata, context }),
        info: (message, data, metadata = {}) =>
            aisp_logInfo(message, data, { ...metadata, context }),
        debug: (message, data, metadata = {}) =>
            aisp_logDebug(message, data, { ...metadata, context }),
        performance: (operation, duration, data = {}) =>
            aisp_logPerformance(operation, duration, { ...data, context }),
        apiCall: (apiName, method, url, options = {}) =>
            aisp_logAPICall(apiName, method, url, { ...options, context }),
        userAction: (action, details = {}) =>
            aisp_logUserAction(action, { ...details, context }),
        functionEntry: (functionName, params = {}) =>
            aisp_logFunctionEntry(functionName, { ...params, context }),
        functionExit: (functionName, result = {}) =>
            aisp_logFunctionExit(functionName, { ...result, context }),
        withTimer: (operation, fn) =>
            aisp_logWithTimer(`${context}: ${operation}`, fn)
    };
}

// 导出全局日志函数（用于其他模块导入）
if (typeof window !== 'undefined') {
    // 在扩展页面中，将函数添加到window对象
    window.aisp_logError = aisp_logError;
    window.aisp_logWarn = aisp_logWarn;
    window.aisp_logInfo = aisp_logInfo;
    window.aisp_logDebug = aisp_logDebug;
    window.aisp_logPerformance = aisp_logPerformance;
    window.aisp_logAPICall = aisp_logAPICall;
    window.aisp_logUserAction = aisp_logUserAction;
    window.aisp_logFunctionEntry = aisp_logFunctionEntry;
    window.aisp_logFunctionExit = aisp_logFunctionExit;
    window.aisp_logWithTimer = aisp_logWithTimer;
    window.aisp_logCreateLogger = aisp_logCreateLogger;
    window.aisp_logSetConfig = aisp_logSetConfig;
    window.aisp_logGetConfig = aisp_logGetConfig;
    window.aisp_logGetStoredLogs = aisp_logGetStoredLogs;
    window.aisp_logClearStoredLogs = aisp_logClearStoredLogs;
    window.aisp_logExportLogs = aisp_logExportLogs;
}

// 初始化日志系统
aisp_logInfo('AI侧边栏日志系统已初始化', { config: aispLogConfig });
