{"extensionName": {"message": "AI Side Panel", "description": "Extension name"}, "extensionDescription": {"message": "AI-powered Chrome side panel extension with intelligent content analysis, multilingual reply suggestions, and quick template features", "description": "Extension description"}, "common": {"ok": {"message": "OK", "description": "OK button"}, "cancel": {"message": "Cancel", "description": "Cancel button"}, "close": {"message": "Close", "description": "Close button"}, "save": {"message": "Save", "description": "Save button"}, "delete": {"message": "Delete", "description": "Delete button"}, "edit": {"message": "Edit", "description": "Edit button"}, "add": {"message": "Add", "description": "Add button"}, "send": {"message": "Send", "description": "Send button"}, "clear": {"message": "Clear", "description": "Clear button"}, "refresh": {"message": "Refresh", "description": "Refresh button"}, "settings": {"message": "Settings", "description": "Settings button"}, "loading": {"message": "Loading...", "description": "Loading status"}, "error": {"message": "Error", "description": "Error message"}, "success": {"message": "Success", "description": "Success message"}}, "sidepanel": {"title": {"message": "AI Side Panel", "description": "Side panel title"}, "welcome": {"message": "Welcome to AI Side Panel", "description": "Welcome message title"}, "welcomeDescription": {"message": "I will help you analyze current page content, provide intelligent reply suggestions and quick template features.", "description": "Welcome message description"}, "analyzeCurrentPage": {"message": "Analyze Current Page", "description": "Analyze current page button"}, "analyzing": {"message": "AI is analyzing...", "description": "Analyzing status"}, "inputPlaceholder": {"message": "Enter your question or content to analyze...", "description": "Input placeholder"}, "connectionStatus": {"connected": {"message": "Connected", "description": "Connected status"}, "disconnected": {"message": "Disconnected", "description": "Disconnected status"}}, "pageInfo": {"waiting": {"message": "Waiting for page to load...", "description": "Waiting for page load"}}}, "analysisPageSummary": {"message": "📄 Page Summary", "description": "Page summary title"}, "analysisKeyPoints": {"message": "🎯 Key Points", "description": "Key points title"}, "analysisMindMap": {"message": "🧠 Mind Map", "description": "Mind map title"}, "analysisReplySuggestions": {"message": "💬 Smart Reply Suggestions", "description": "Smart reply suggestions title"}, "analysisNoSummary": {"message": "No summary available", "description": "No summary message"}, "analysisNoKeyPoints": {"message": "No key points available", "description": "No key points message"}, "templates": {"title": {"message": "Quick Reply Templates", "description": "Template popup title"}, "hint": {"message": "Click to select template, press Tab to insert", "description": "Template usage hint"}, "manager": {"message": "Template Manager", "description": "Template manager button"}, "untitled": {"message": "Untitled Template", "description": "Untitled template"}, "developing": {"message": "Template management feature is under development...", "description": "Under development message"}}, "knowledgeBase": {"title": {"message": "Knowledge Base", "description": "Knowledge base title"}, "developing": {"message": "Knowledge base feature is under development...", "description": "Under development message"}}, "popup": {"openSidePanel": {"message": "Open Side Panel", "description": "Open side panel button"}, "analyzePage": {"message": "Analyze Current Page", "description": "Analyze page button"}, "aiStatus": {"message": "AI Analysis", "description": "AI status label"}, "templateStatus": {"message": "Template System", "description": "Template status label"}, "syncStatus": {"message": "Cloud Sync", "description": "Sync status label"}, "autoAnalysis": {"message": "Auto analyze page content", "description": "Auto analysis option"}, "enableTemplate": {"message": "Enable quick templates", "description": "Enable template option"}, "languageSettings": {"message": "Language Settings", "description": "Language settings label"}, "detailedSettings": {"message": "Detailed Settings", "description": "Detailed settings link"}, "help": {"message": "Help", "description": "Help link"}, "configured": {"message": "Configured", "description": "Configured status"}, "notConfigured": {"message": "Not Configured", "description": "Not configured status"}, "enabled": {"message": "Enabled", "description": "Enabled status"}, "disabled": {"message": "Disabled", "description": "Disabled status"}}, "errors": {"initializationFailed": {"message": "Initialization failed, please refresh and try again", "description": "Initialization failed error"}, "analysisFailed": {"message": "Page analysis failed", "description": "Analysis failed error"}, "languageChangeFailed": {"message": "Language change failed", "description": "Language change failed error"}, "messageSendFailed": {"message": "Message send failed", "description": "Message send failed error"}, "openSidePanelFailed": {"message": "Failed to open side panel, please try again", "description": "Open side panel failed error"}, "settingsSaveFailed": {"message": "Settings save failed", "description": "Settings save failed error"}}, "success": {"analysisStarted": {"message": "Page analysis started", "description": "Analysis started success"}, "autoAnalysisEnabled": {"message": "Auto analysis enabled", "description": "Auto analysis enabled success"}, "autoAnalysisDisabled": {"message": "Auto analysis disabled", "description": "Auto analysis disabled success"}, "templateEnabled": {"message": "Quick templates enabled", "description": "Template enabled success"}, "templateDisabled": {"message": "Quick templates disabled", "description": "Template disabled success"}, "languageSaved": {"message": "Language settings saved", "description": "Language saved success"}}}